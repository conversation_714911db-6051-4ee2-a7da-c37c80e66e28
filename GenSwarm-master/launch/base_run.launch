<launch>
    <!-- 从 YAML 文件加载参数 -->
    <rosparam file="$(find your_package)/config/config.yaml" />

    <!-- 启动第一个节点 -->
    <node name="node1" pkg="example_package" type="example_node1" output="screen">
        <param name="param1" value="$(param node1/param1)" />
        <param name="param2" value="$(param node1/param2)" />
    </node>

    <!-- 启动第二个节点 -->
    <node name="node2" pkg="example_package" type="example_node2" output="screen">
        <param name="param1" value="$(param node2/param1)" />
        <param name="param2" value="$(param node2/param2)" />
    </node>
</launch>
