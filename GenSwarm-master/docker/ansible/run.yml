- name: Start LLM code
  command: >
    nvidia-docker exec -it -d
        llm-code
        bash -c "source /catkin_ws/devel/setup.bash && roslaunch code_llm run.launch && tail -f /dev/null"
  become: true
  become_user: nvidia  # 替换为你的 ROS 用户
  register: llm_code_info

- name: Show container logs
  debug:
    var: llm_code_info.stdout_lines

#
#- name: Show container logs
#  debug:
#    var: MY_VARIABLE
#- name: Remove docker container
#  command: >
#    nvidia-docker container remove llm-code

#- name: Start LLM code
#  command: >
#    nvidia-docker run  --rm
#        --name llm-code
#        -v /home/<USER>/docker/code_llm_ws:/catkin_ws
#        --workdir /catkin_ws
#        --network host
#        llm_simulator
#        bash -c "source /catkin_ws/devel/setup.bash && roslaunch code_llm run.launch"
#  #    async: 3000  # 设置任务的超时时间（秒），这里设置为 300 秒（5分钟）
#  #    poll: 0  # 立即返回，不等待任务完成
#  become: true
#  become_user: nvidia  # 替换为你的 ROS 用户
#  register: llm_code_info
#
#- name: Show container logs
#  debug:
#    var: llm_code_info.stdout_lines
#

#- name: Start LLM code
#  command: pwd
#  become: true
#  become_user: nvidia  # 替换为你的 ROS 用户
#  register: llm_code_info

#
#- name: Show container logs
#  debug:
#    var: MY_VARIABLE
