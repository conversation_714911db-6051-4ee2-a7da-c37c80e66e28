version: '3.8'

services:
  base:
    build:
      context: .
      dockerfile: base.Dockerfile
    image: huabench/code-llm:base

  runtime-base:
    extends:
      service: base
    build:
      context: ..
      dockerfile: ./docker/runtime.Dockerfile
    image: huabench/code-llm:runtime
    volumes:
      - ..:/catkin_ws/src/code_llm
    network_mode: host
    stdin_open: true
    tty: true

  unittest:
    extends:
      service: runtime-base
    command:
      - /bin/bash
      - 、=】
      - -c
      - |
        source /opt/ros/noetic/setup.bash && catkin_make -DPYTHON_EXECUTABLE=/usr/bin/python3 && source ./devel/setup.bash && cd ./src/code_llm &&
        conda run --no-capture-output -n py310 \
        python -m unittest discover tests

  deploy:
    extends:
      service: base
    build:
      context: ..
      dockerfile: ./docker/deploy.Dockerfile
    image: huabench/code-llm:deploy
    working_dir: /src
    command: ansible-playbook ./docker/ansible/main.yml -e DATA_PATH=${DATA_PATH} -e STAGE=1
    # command: /bin/bash
    volumes:
      - ..:/src
      - ../config/hosts:/etc/ansible/hosts
    network_mode: host
    deploy:
      restart_policy:
        condition: none
