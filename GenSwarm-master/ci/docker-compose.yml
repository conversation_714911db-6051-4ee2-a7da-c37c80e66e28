version: '3'

services:
  unittest:
    image: huabench/code-llm:runtime
    command:
      # - /bin/bash
      - /bin/bash
      - -c
      - |
        source /opt/ros/noetic/setup.bash && catkin_make -DPYTHON_EXECUTABLE=/usr/bin/python3 && source ./devel/setup.bash && cd ./src/code_llm &&
        conda run --no-capture-output -n py310 coverage run -m unittest discover tests && \
        conda run --no-capture-output -n py310 coverage report --ignore-errors && \
        conda run --no-capture-output -n py310 coverage xml --ignore-errors
    volumes:
      - ..:/catkin_ws/src/code_llm
    environment:
      - API_KEY=  ${API_KEY}
      - API_BASE=  ${API_BASE}

    network_mode: host
    stdin_open: true
    tty: true

  python_build:
    build:
      context: ..
      dockerfile: ./docker/runtime.Dockerfile
      args:
        PYTHON_VERSION: ${PYTHON_VERSION}
    image: huabench/code-llm:runtime_${PYTHON_VERSION}
    volumes:
      - ..:/src
    command:
      - /bin/bash
      - -c
      - |
        source /opt/ros/noetic/setup.bash && catkin_make -DPYTHON_EXECUTABLE=/usr/bin/python3 && source ./devel/setup.bash && cd ./src/code_llm &&
        conda run --no-capture-output -n py$$(echo $PYTHON_VERSION | sed 's/\.//g') \
        python -m unittest discover
