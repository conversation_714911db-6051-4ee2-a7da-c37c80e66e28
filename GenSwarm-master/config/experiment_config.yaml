arguments:
  --run_mode:
    default: run_experiment
    help: options:generate_code,run_test,run_experiment
  #  --test_dir_name:
  #    default: 'test'
  #    help: special for test_code mode to specify the test dir name
  --experiment_nums:
    default: 1
    help: special for generate_code mode to specify the experiment nums
  --run_experiment_name:
    default: [ 'covering' ]
    help: for run_experiment mode and generate_code mode to specify the experiment name
  --run_times:
    default: 60
    help: simulation times when run_test mode
  --run_code:
    default: False
  --video_critic:
    default: False
  --human_feedback:
    default: False
#  --generate_mode:
#    default: 'layer'
#    help: options:'parallel','sequential','layer'
