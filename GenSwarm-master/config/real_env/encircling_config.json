{"display": {"#width and height note": "unit: meter", "width": 5, "height": 5, "#scale_factor_note": "1 meter = 100 pixel", "scale_factor": 100}, "task_name": "encircling", "#entity note": "position/size/perceptual_range unit: meter", "#size note": "the radium of the circle # | the side length of the square robot", "#agent number note": "sum of robot + leader", "entities": {"robot": {"count": 5, "specified": [], "size": 0.15, "color": "green", "shape": "circle", "perceptual_range": 1, "id_list": [3, 6, 5, 8, 9]}, "leader": {"count": 0, "specified": []}, "obstacle": {"count": 2, "specified": [], "size": 0.15, "shape": "circle", "color": "green", "id_list": [4, 9]}, "sheep": {"count": 0, "specified": []}, "prey": {"count": 1, "specified": [], "id_list": [10]}, "landmark": {"count": 0, "specified": []}, "pushable_object": {"count": 0, "specified": []}}, "engine_type": "OmniEngine", "render_mode": "human", "output_file": "output.json", "dt": 0.1}