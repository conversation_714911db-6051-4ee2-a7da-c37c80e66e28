{"display": {"#width and height note": "unit: meter", "width": 5, "height": 5, "#scale_factor_note": "1 meter = 100 pixel", "scale_factor": 100}, "task_name": "shaping", "#entity note": "position/size/perceptual_range unit: meter", "#size note": "the radium of the circle # | the side length of the square robot", "#agent number note": "sum of robot + leader", "entities": {"robot": {"count": 5, "specified": [], "size": 0.12, "color": "green", "shape": "circle", "perceptual_range": 1, "id_list": [5, 6, 7, 8, 9, 10]}, "leader": {"count": 0, "specified": []}, "obstacle": {"count": 0, "specified": [], "size": 0.15, "shape": "circle", "color": "green"}, "sheep": {"count": 0, "specified": []}, "landmark": {"count": 0, "specified": []}, "pushable_object": {"count": 0, "specified": []}}, "engine_type": "OmniEngine", "render_mode": "human", "output_file": "output.json", "dt": 0.1}